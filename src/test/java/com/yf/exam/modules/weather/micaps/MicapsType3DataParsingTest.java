package com.yf.exam.modules.weather.micaps;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 第三类MDFS数据解析测试
 * 专门用于测试第三类数据的解析功能
 */
@SpringBootTest
public class MicapsType3DataParsingTest {

    @Autowired
    private MicapsDataService micapsDataService;

    @Test
    public void testType3DataParsing() {
        // 这个测试需要真实的第三类MDFS文件
        // 由于没有具体的测试文件，这里只是验证解析器不会崩溃
        
        try {
            // 创建一个不存在的文件来测试错误处理
            File nonExistentFile = new File("non_existent_type3_file.mdfs");
            
            // 这应该会抛出异常或返回空数据，但不应该崩溃
            MicapsData result = micapsDataService.parseMicapsData(nonExistentFile);
            
            // 如果返回了结果，验证它是正确的类型
            if (result != null) {
                assertTrue(result instanceof MicapsType3Data || result.getDataType() == 3);
            }
            
        } catch (Exception e) {
            // 预期会有异常，因为文件不存在
            assertTrue(e instanceof IOException || e instanceof IllegalArgumentException);
        }
    }

    @Test
    public void testType3DataStructure() {
        // 测试第三类数据结构的基本功能
        MicapsType3Data data = new MicapsType3Data();
        data.setDataType(3);
        data.setDescription("测试第三类数据");
        data.setYear(2025);
        data.setMonth(1);
        data.setDay(30);
        data.setHour(8);
        data.setLevel(-3); // 温度格式
        data.setContourCount(5);
        data.setTotalStations(10);

        // 验证基本属性
        assertEquals(3, data.getDataType());
        assertEquals("测试第三类数据", data.getDescription());
        assertEquals(2025, data.getYear());
        assertEquals(1, data.getMonth());
        assertEquals(30, data.getDay());
        assertEquals(8, data.getHour());
        assertEquals(-3, data.getLevel());
        assertEquals("温度格式", data.getFormatDescription());
        assertTrue(data.supportsContourDrawing());
        assertFalse(data.hasClipRegion());
    }

    @Test
    public void testType3StationData() {
        // 测试第三类站点数据
        MicapsType3Data.MicapsType3Station station = new MicapsType3Data.MicapsType3Station();
        station.setStationId(52533L);
        station.setLongitude(98.48);
        station.setLatitude(39.77);
        station.setElevation(1478.0);
        station.setValue1("16");
        station.setValue2("20");

        // 验证站点数据
        assertEquals(52533L, station.getStationId());
        assertEquals(98.48, station.getLongitude(), 0.01);
        assertEquals(39.77, station.getLatitude(), 0.01);
        assertEquals(1478.0, station.getElevation(), 0.1);
        assertEquals("16", station.getValue1());
        assertEquals("20", station.getValue2());
        assertEquals(16.0, station.getNumericValue1(), 0.01);
        assertEquals(20.0, station.getNumericValue2(), 0.01);
        assertTrue(station.isValid());
    }

    @Test
    public void testClipBoundaryPoint() {
        // 测试剪切区域边界点
        MicapsType3Data.ClipBoundaryPoint point = new MicapsType3Data.ClipBoundaryPoint(120.5, 35.8);
        
        assertEquals(120.5, point.getLongitude(), 0.01);
        assertEquals(35.8, point.getLatitude(), 0.01);
    }
}
