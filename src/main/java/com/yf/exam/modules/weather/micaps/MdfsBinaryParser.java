package com.yf.exam.modules.weather.micaps;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.*;

/**
 * MDFS二进制数据解析器
 * 专门用于解析MICAPS MDFS文件的二进制数据部分
 * 
 * 参考：https://github.com/CyanideCN/micaps_mdfs
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Component
public class MdfsBinaryParser {
    
    /**
     * 解析第三类MDFS数据的二进制部分：非规范站点填图数据
     *
     * @param file 文件对象
     * @param header 文件头信息
     * @return 第三类数据
     * @throws IOException 读取异常
     */
    public MicapsType3Data parseType3BinaryData(File file, MdfsFileHeader header) throws IOException {
        log.info("开始解析第三类MDFS数据的二进制部分");

        MicapsType3Data data = new MicapsType3Data();
        data.setDataType(3);
        data.setDescription(header.getDescription());

        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // MDFS文件从头开始解析，不依赖文本头部
            raf.seek(0);

            // 验证MDFS魔数
            byte[] magic = new byte[4];
            raf.read(magic);
            String magicStr = new String(magic);
            if (!"mdfs".equals(magicStr)) {
                throw new IOException("不是有效的MDFS文件，魔数: " + magicStr);
            }

            // 解析第三类MDFS文件结构
            MdfsType3Structure fileStructure = parseMdfsType3Structure(raf);
            log.info("第三类MDFS文件结构: {}", fileStructure);

            // 设置基本信息
            data.setDescription(fileStructure.getDescription());
            data.setYear(fileStructure.getYear());
            data.setMonth(fileStructure.getMonth());
            data.setDay(fileStructure.getDay());
            data.setHour(fileStructure.getHour());
            data.setLevel(fileStructure.getLevel());
            data.setContourCount(fileStructure.getContourCount());
            data.setContourValue1(fileStructure.getContourValue1());
            data.setContourValue2(fileStructure.getContourValue2());
            data.setSmoothFactor(fileStructure.getSmoothFactor());
            data.setBoldLineValue(fileStructure.getBoldLineValue());
            data.setClipBoundaryPointCount(fileStructure.getClipBoundaryPointCount());
            data.setElementCount(fileStructure.getElementCount());
            data.setTotalStations(fileStructure.getTotalStations());

            // 读取剪切区域边界点
            List<MicapsType3Data.ClipBoundaryPoint> clipPoints = readClipBoundaryPoints(raf, fileStructure);
            data.setClipBoundaryPoints(clipPoints);

            // 读取站点数据
            List<MicapsType3Data.MicapsType3Station> stations = readType3StationData(raf, fileStructure);
            data.setStations(stations);

            log.info("成功解析第三类数据: {} 个站点", stations.size());
            return data;

        } catch (Exception e) {
            log.error("解析第三类MDFS数据失败", e);

            // 返回空数据结构，避免程序崩溃
            data.setTotalStations(0);
            data.setStations(new ArrayList<>());
            return data;
        }
    }

    /**
     * 解析MDFS站点数据的二进制部分
     * 根据Python实现重写，遵循正确的MDFS格式
     *
     * @param file 文件对象
     * @param header 文件头信息
     * @return 站点数据
     * @throws IOException 读取异常
     */
    public MicapsType1Data parseStationBinaryData(File file, MdfsFileHeader header) throws IOException {
        log.info("开始解析MDFS站点数据的二进制部分");

        MicapsType1Data data = new MicapsType1Data();
        data.setDataType(1);
        data.setDescription(header.getDescription());

        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // MDFS文件从头开始解析，不依赖文本头部
            raf.seek(0);

            // 验证MDFS魔数
            byte[] magic = new byte[4];
            raf.read(magic);
            String magicStr = new String(magic);
            if (!"mdfs".equals(magicStr)) {
                throw new IOException("不是有效的MDFS文件，魔数: " + magicStr);
            }

            // 解析MDFS文件头
            MdfsFileStructure fileStructure = parseMdfsFileStructure(raf);
            log.info("MDFS文件结构: {}", fileStructure);

            // 读取站点数据
            List<MicapsStation> stations = readMdfsStationData(raf, fileStructure);

            data.setTotalStations(stations.size());
            data.setStations(stations);
            data.setYear(fileStructure.getYear());
            data.setMonth(fileStructure.getMonth());
            data.setDay(fileStructure.getDay());
            data.setHour(fileStructure.getHour());

            log.info("成功解析 {} 个站点的数据", stations.size());
            return data;

        } catch (Exception e) {
            log.error("解析MDFS站点数据失败", e);

            // 返回空数据结构，避免程序崩溃
            data.setTotalStations(0);
            data.setStations(new ArrayList<>());
            return data;
        }
    }
    
    /**
     * 解析MDFS文件结构（按照Python实现的正确格式）
     */
    private MdfsFileStructure parseMdfsFileStructure(RandomAccessFile raf) throws IOException {
        MdfsFileStructure structure = new MdfsFileStructure();

        // 读取数据类型 (2 bytes, short)
        structure.setDataType(readShort(raf));

        // 读取描述信息 (100 bytes, GBK编码)
        byte[] descBytes = new byte[100];
        raf.read(descBytes);
        structure.setDescription(new String(descBytes, "GBK").replace("\0", "").trim());

        // 读取层面信息 (4 bytes, float)
        structure.setLevel(readFloat(raf));

        // 读取层面描述 (50 bytes, GBK编码)
        byte[] levelDescBytes = new byte[50];
        raf.read(levelDescBytes);
        structure.setLevelDescription(new String(levelDescBytes, "GBK").replace("\0", "").trim());

        // 读取时间信息 (7 * 4 = 28 bytes, 7个int)
        structure.setYear(readInt(raf));
        structure.setMonth(readInt(raf));
        structure.setDay(readInt(raf));
        structure.setHour(readInt(raf));
        structure.setMinute(readInt(raf));
        structure.setSecond(readInt(raf));
        structure.setTimezone(readInt(raf));

        // 读取ID类型 (2 bytes, short)
        structure.setIdType(readShort(raf));

        // 跳过填充字节到位置288 (98 bytes)
        raf.skipBytes(98);

        // 读取站点数量 (4 bytes, int)
        structure.setStationCount(readInt(raf));

        // 读取要素数量 (2 bytes, short)
        structure.setQuantityCount(readShort(raf));

        log.debug("MDFS文件结构解析完成: 数据类型={}, 站点数={}, 要素数={}",
            structure.getDataType(), structure.getStationCount(), structure.getQuantityCount());

        return structure;
    }
    
    /**
     * 读取MDFS站点数据（按照Python实现的正确格式）
     */
    private List<MicapsStation> readMdfsStationData(RandomAccessFile raf, MdfsFileStructure structure) throws IOException {
        List<MicapsStation> stations = new ArrayList<>();

        // 读取要素信息映射
        Map<Integer, Integer> quantityMap = new HashMap<>();
        for (int i = 0; i < structure.getQuantityCount(); i++) {
            int varId = readShort(raf);
            int varType = readShort(raf);
            quantityMap.put(varId, varType);
        }

        // 读取每个站点的数据
        for (int stationIdx = 0; stationIdx < structure.getStationCount(); stationIdx++) {
            try {
                MicapsStation station = readSingleMdfsStation(raf, structure, quantityMap);
                if (station != null && isValidStation(station)) {
                    stations.add(station);
                }
            } catch (Exception e) {
                log.debug("读取第{}个站点数据失败: {}", stationIdx + 1, e.getMessage());
                break; // 遇到错误就停止读取
            }
        }

        return stations;
    }

    /**
     * 读取单个MDFS站点数据
     */
    private MicapsStation readSingleMdfsStation(RandomAccessFile raf, MdfsFileStructure structure,
                                               Map<Integer, Integer> quantityMap) throws IOException {
        MicapsStation station = new MicapsStation();

        // 根据ID类型读取站点ID
        if (structure.getIdType() != 1) {
            // 数字ID (4 bytes)
            station.setStationId((long) readInt(raf));
        } else {
            // 字符串ID
            int idLength = readShort(raf);
            byte[] idBytes = new byte[idLength];
            raf.read(idBytes);
            // 对于字符串ID，我们尝试转换为数字，如果失败则使用哈希值
            String idStr = new String(idBytes, "GBK");
            try {
                station.setStationId(Long.parseLong(idStr));
            } catch (NumberFormatException e) {
                station.setStationId((long) idStr.hashCode());
            }
        }

        // 读取经纬度 (4 bytes each, float)
        station.setLongitude((double) readFloat(raf));
        station.setLatitude((double) readFloat(raf));

        // 读取要素数量
        int qNum = readShort(raf);

        // 读取各个要素的值
        for (int i = 0; i < qNum; i++) {
            int varId = readShort(raf);

            // 根据要素ID确定数据类型和读取方式
            Object value = readVariableValue(raf, varId, quantityMap);

            // 根据要素ID设置到相应的字段
            setStationVariable(station, varId, value);
        }

        return station;
    }
    
    /**
     * 读取变量值（根据变量ID和类型）
     */
    private Object readVariableValue(RandomAccessFile raf, int varId, Map<Integer, Integer> quantityMap) throws IOException {
        int varType;

        // 质量控制代码的特殊处理
        if (varId % 2 == 0 && varId >= 22) {
            varType = 1; // 质量控制代码使用类型1
        } else {
            varType = quantityMap.getOrDefault(varId, getDefaultVariableType(varId));
        }

        // 根据类型读取数据
        switch (varType) {
            case 1: // byte
                return (int) raf.readByte();
            case 2: // short
                return (int) readShort(raf);
            case 3: // int
                return readInt(raf);
            case 4: // long
                return readLong(raf);
            case 5: // float
                return readFloat(raf);
            case 6: // double
                return readDouble(raf);
            case 7: // string (1 byte length)
                int len = raf.readByte() & 0xFF;
                byte[] strBytes = new byte[len];
                raf.read(strBytes);
                return new String(strBytes, "GBK");
            default:
                // 默认按float处理
                return readFloat(raf);
        }
    }

    /**
     * 获取变量的默认类型（基于常见的气象要素ID）
     */
    private int getDefaultVariableType(int varId) {
        // 这里应该根据实际的MDFS规范来定义
        // 暂时使用简化的映射
        switch (varId) {
            case 1: case 3: case 5: case 7: case 9: // 温度相关
            case 11: case 13: case 15: case 17: // 湿度相关
            case 31: case 33: case 35: // 风速相关
            case 601: case 603: case 605: // 降水相关
                return 5; // float
            case 41: case 43: case 45: // 风向相关
                return 3; // int
            default:
                return 5; // 默认float
        }
    }
    
    /**
     * 根据变量ID设置站点变量值
     */
    private void setStationVariable(MicapsStation station, int varId, Object value) {
        if (value == null) return;

        // 根据常见的气象要素ID进行映射
        // 这里的映射需要根据实际的MDFS规范来完善
        switch (varId) {
            case 1: case 3: case 5: // 温度相关
                if (value instanceof Number) {
                    station.setTemperature(((Number) value).doubleValue());
                }
                break;
            case 7: case 9: // 气压相关
                if (value instanceof Number) {
                    station.setPressure(((Number) value).doubleValue());
                }
                break;
            case 31: case 33: // 风速相关
                if (value instanceof Number) {
                    station.setWindSpeed(((Number) value).intValue());
                }
                break;
            case 41: case 43: // 风向相关
                if (value instanceof Number) {
                    station.setWindDirection(((Number) value).intValue());
                }
                break;
            case 601: case 603: case 605: // 降水相关
                if (value instanceof Number) {
                    double precip = ((Number) value).doubleValue();
                    if (precip >= 0 && precip < 9999) { // 过滤异常值
                        station.setPrecipitation6h(precip);
                    }
                }
                break;
            case 15: case 17: // 能见度相关
                if (value instanceof Number) {
                    station.setVisibility(((Number) value).doubleValue());
                }
                break;
            default:
                // 对于未知的变量ID，记录日志但不处理
                log.debug("未处理的变量ID: {} = {}", varId, value);
                break;
        }
    }
    
    // 辅助方法：读取基本数据类型（小端序）
    private short readShort(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[2];
        raf.read(bytes);
        return (short) ((bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8));
    }

    private int readInt(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[4];
        raf.read(bytes);
        return (bytes[0] & 0xFF) |
               ((bytes[1] & 0xFF) << 8) |
               ((bytes[2] & 0xFF) << 16) |
               ((bytes[3] & 0xFF) << 24);
    }

    private long readLong(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[8];
        raf.read(bytes);
        long result = 0;
        for (int i = 0; i < 8; i++) {
            result |= ((long) (bytes[i] & 0xFF)) << (i * 8);
        }
        return result;
    }

    private float readFloat(RandomAccessFile raf) throws IOException {
        int intBits = readInt(raf);
        return Float.intBitsToFloat(intBits);
    }

    private double readDouble(RandomAccessFile raf) throws IOException {
        long longBits = readLong(raf);
        return Double.longBitsToDouble(longBits);
    }

    /**
     * 验证站点数据是否有效
     */
    private boolean isValidStation(MicapsStation station) {
        return station != null &&
               station.getStationId() > 0 &&
               Math.abs(station.getLongitude()) <= 180 &&
               Math.abs(station.getLatitude()) <= 90;
    }
    
    /**
     * MDFS文件结构信息（按照Python实现的正确格式）
     */
    private static class MdfsFileStructure {
        private int dataType;
        private String description;
        private float level;
        private String levelDescription;
        private int year, month, day, hour, minute, second, timezone;
        private int idType;
        private int stationCount;
        private int quantityCount;

        // Getters and Setters
        public int getDataType() { return dataType; }
        public void setDataType(int dataType) { this.dataType = dataType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public float getLevel() { return level; }
        public void setLevel(float level) { this.level = level; }
        public String getLevelDescription() { return levelDescription; }
        public void setLevelDescription(String levelDescription) { this.levelDescription = levelDescription; }
        public int getYear() { return year; }
        public void setYear(int year) { this.year = year; }
        public int getMonth() { return month; }
        public void setMonth(int month) { this.month = month; }
        public int getDay() { return day; }
        public void setDay(int day) { this.day = day; }
        public int getHour() { return hour; }
        public void setHour(int hour) { this.hour = hour; }
        public int getMinute() { return minute; }
        public void setMinute(int minute) { this.minute = minute; }
        public int getSecond() { return second; }
        public void setSecond(int second) { this.second = second; }
        public int getTimezone() { return timezone; }
        public void setTimezone(int timezone) { this.timezone = timezone; }
        public int getIdType() { return idType; }
        public void setIdType(int idType) { this.idType = idType; }
        public int getStationCount() { return stationCount; }
        public void setStationCount(int stationCount) { this.stationCount = stationCount; }
        public int getQuantityCount() { return quantityCount; }
        public void setQuantityCount(int quantityCount) { this.quantityCount = quantityCount; }

        @Override
        public String toString() {
            return String.format("MdfsFileStructure[type=%d, desc='%s', stations=%d, quantities=%d, time=%04d-%02d-%02d %02d:%02d:%02d]",
                dataType, description, stationCount, quantityCount, year, month, day, hour, minute, second);
        }
    }

    /**
     * 第三类MDFS文件结构信息
     */
    private static class MdfsType3Structure {
        private String description;
        private int year, month, day, hour, level;
        private int contourCount;
        private double contourValue1, contourValue2, smoothFactor, boldLineValue;
        private int clipBoundaryPointCount;
        private int elementCount, totalStations;

        // Getters and Setters
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public int getYear() { return year; }
        public void setYear(int year) { this.year = year; }
        public int getMonth() { return month; }
        public void setMonth(int month) { this.month = month; }
        public int getDay() { return day; }
        public void setDay(int day) { this.day = day; }
        public int getHour() { return hour; }
        public void setHour(int hour) { this.hour = hour; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
        public int getContourCount() { return contourCount; }
        public void setContourCount(int contourCount) { this.contourCount = contourCount; }
        public double getContourValue1() { return contourValue1; }
        public void setContourValue1(double contourValue1) { this.contourValue1 = contourValue1; }
        public double getContourValue2() { return contourValue2; }
        public void setContourValue2(double contourValue2) { this.contourValue2 = contourValue2; }
        public double getSmoothFactor() { return smoothFactor; }
        public void setSmoothFactor(double smoothFactor) { this.smoothFactor = smoothFactor; }
        public double getBoldLineValue() { return boldLineValue; }
        public void setBoldLineValue(double boldLineValue) { this.boldLineValue = boldLineValue; }
        public int getClipBoundaryPointCount() { return clipBoundaryPointCount; }
        public void setClipBoundaryPointCount(int clipBoundaryPointCount) { this.clipBoundaryPointCount = clipBoundaryPointCount; }
        public int getElementCount() { return elementCount; }
        public void setElementCount(int elementCount) { this.elementCount = elementCount; }
        public int getTotalStations() { return totalStations; }
        public void setTotalStations(int totalStations) { this.totalStations = totalStations; }

        @Override
        public String toString() {
            return String.format("MdfsType3Structure[desc='%s', time=%04d-%02d-%02d %02d, level=%d, stations=%d, contours=%d]",
                description, year, month, day, hour, level, totalStations, contourCount);
        }
    }

    /**
     * 解析第三类数据文件结构（完全二进制格式）
     * 根据MDFS第三类数据格式说明重新实现
     */
    private MdfsType3Structure parseMdfsType3Structure(RandomAccessFile raf) throws IOException {
        MdfsType3Structure structure = new MdfsType3Structure();

        // 解析MDFS文件头（与第一类相同的结构）
        MdfsFileStructure mdfsHeader = parseMdfsFileStructure(raf);

        // 从MDFS头部提取基本信息
        structure.setDescription(mdfsHeader.getDescription());
        structure.setYear(mdfsHeader.getYear());
        structure.setMonth(mdfsHeader.getMonth());
        structure.setDay(mdfsHeader.getDay());
        structure.setHour(mdfsHeader.getHour());
        structure.setLevel((int)mdfsHeader.getLevel()); // 层次信息

        // 读取第三类数据特有的头部信息
        // 等值线条数 (4 bytes, int)
        structure.setContourCount(readInt(raf));

        // 等值线值1、等值线值2、平滑系数、加粗线值 (4 * 4 = 16 bytes, 4个float)
        structure.setContourValue1(readFloat(raf));
        structure.setContourValue2(readFloat(raf));
        structure.setSmoothFactor(readFloat(raf));
        structure.setBoldLineValue(readFloat(raf));

        // 剪切区域边缘线上的点数 (4 bytes, int)
        structure.setClipBoundaryPointCount(readInt(raf));

        // 单站填图要素的个数和总站点数 (2 * 4 = 8 bytes, 2个int)
        structure.setElementCount(readInt(raf));
        structure.setTotalStations(readInt(raf));

        log.info("第三类数据文件结构解析完成: {}", structure);
        return structure;
    }

    /**
     * 解析第三类数据的混合头部（第一行文本 + 后续二进制）
     */
    private void parseType3TextHeader(RandomAccessFile raf, MdfsType3Structure structure) throws IOException {
        // 只读取第一行文本：diamond  3  数据说明  年  月  日  时次  层次
        String line1 = readTextLine(raf);
        String[] parts1 = line1.trim().split("\\s+");
        if (parts1.length >= 7) {
            // parts1[0] = "diamond"
            // parts1[1] = "3" (数据类型)
            // parts1[2] = 数据说明
            structure.setDescription(parts1[2]);
            structure.setYear(Integer.parseInt(parts1[3]));
            structure.setMonth(Integer.parseInt(parts1[4]));
            structure.setDay(Integer.parseInt(parts1[5]));
            structure.setHour(Integer.parseInt(parts1[6]));
            if (parts1.length > 7) {
                structure.setLevel(Integer.parseInt(parts1[7]));
            }
        }

        log.debug("解析第一行文本完成: 描述={}, 时间={}-{}-{} {}时, 层次={}",
            structure.getDescription(), structure.getYear(), structure.getMonth(),
            structure.getDay(), structure.getHour(), structure.getLevel());

        // 从第二行开始是二进制格式，按照第三类数据的二进制结构读取

        // 读取年月日时层次 (5 * 4 = 20 bytes, 5个int)
        int year = readInt(raf);
        int month = readInt(raf);
        int day = readInt(raf);
        int hour = readInt(raf);
        int level = readInt(raf);

        // 验证二进制数据与文本头部的一致性
        if (year != structure.getYear() || month != structure.getMonth() ||
            day != structure.getDay() || hour != structure.getHour()) {
            log.warn("二进制时间数据与文本头部不一致: 文本({}-{}-{} {}时) vs 二进制({}-{}-{} {}时)",
                structure.getYear(), structure.getMonth(), structure.getDay(), structure.getHour(),
                year, month, day, hour);
        }

        // 使用二进制数据更新结构（更准确）
        structure.setYear(year);
        structure.setMonth(month);
        structure.setDay(day);
        structure.setHour(hour);
        structure.setLevel(level);

        // 读取等值线条数 (4 bytes, int)
        structure.setContourCount(readInt(raf));

        // 读取等值线值1、等值线值2、平滑系数、加粗线值 (4 * 4 = 16 bytes, 4个float)
        structure.setContourValue1(readFloat(raf));
        structure.setContourValue2(readFloat(raf));
        structure.setSmoothFactor(readFloat(raf));
        structure.setBoldLineValue(readFloat(raf));

        // 读取剪切区域边缘线上的点数 (4 bytes, int)
        structure.setClipBoundaryPointCount(readInt(raf));

        // 读取单站填图要素的个数和总站点数 (2 * 4 = 8 bytes, 2个int)
        structure.setElementCount(readInt(raf));
        structure.setTotalStations(readInt(raf));

        log.debug("解析第三类数据头部完成: 描述={}, 时间={}-{}-{} {}时, 层次={}, 等值线条数={}, 站点数={}",
            structure.getDescription(), structure.getYear(), structure.getMonth(),
            structure.getDay(), structure.getHour(), structure.getLevel(),
            structure.getContourCount(), structure.getTotalStations());

        // 验证数据合理性
        if (structure.getTotalStations() <= 0 || structure.getTotalStations() > 100000) {
            log.warn("总站点数异常: {}, 可能存在解析错误", structure.getTotalStations());
        }

        if (structure.getContourCount() < 0 || structure.getContourCount() > 1000) {
            log.warn("等值线条数异常: {}, 可能存在解析错误", structure.getContourCount());
        }
    }

    /**
     * 读取GBK编码的文本行
     */
    private String readTextLine(RandomAccessFile raf) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int b;

        while ((b = raf.read()) != -1) {
            if (b == '\n') {
                break;
            }
            if (b == '\r') {
                // 检查是否为\r\n
                long pos = raf.getFilePointer();
                if (raf.read() != '\n') {
                    raf.seek(pos);
                }
                break;
            }
            baos.write(b);
        }

        // 使用GBK编码解码字节数组
        try {
            return new String(baos.toByteArray(), "GBK").trim();
        } catch (UnsupportedEncodingException e) {
            // 如果GBK不支持，回退到默认编码
            log.warn("GBK编码不支持，使用默认编码");
            return new String(baos.toByteArray()).trim();
        }
    }



    /**
     * 读取剪切区域边界点
     * 注意：第三类数据的剪切区域边界点在文本头部中，不在二进制部分
     */
    private List<MicapsType3Data.ClipBoundaryPoint> readClipBoundaryPoints(RandomAccessFile raf, MdfsType3Structure structure) throws IOException {
        List<MicapsType3Data.ClipBoundaryPoint> points = new ArrayList<>();

        // 第三类数据的剪切区域边界点已经在文本头部解析时处理了
        // 这里返回空列表，实际的边界点需要在parseType3TextHeader中解析

        log.debug("第三类数据的剪切区域边界点在文本头部中，返回空列表");
        return points;
    }

    /**
     * 读取第三类站点数据
     */
    private List<MicapsType3Data.MicapsType3Station> readType3StationData(RandomAccessFile raf, MdfsType3Structure structure) throws IOException {
        List<MicapsType3Data.MicapsType3Station> stations = new ArrayList<>();

        for (int i = 0; i < structure.getTotalStations(); i++) {
            try {
                MicapsType3Data.MicapsType3Station station = readSingleType3Station(raf, structure);
                if (station != null && station.isValid()) {
                    stations.add(station);
                }
            } catch (Exception e) {
                log.debug("读取第{}个第三类站点数据失败: {}", i + 1, e.getMessage());
                break;
            }
        }

        return stations;
    }

    /**
     * 读取单个第三类站点数据
     */
    private MicapsType3Data.MicapsType3Station readSingleType3Station(RandomAccessFile raf, MdfsType3Structure structure) throws IOException {
        MicapsType3Data.MicapsType3Station station = new MicapsType3Data.MicapsType3Station();

        // 读取区站号 (4 bytes, int)
        station.setStationId(readInt(raf));

        // 读取经纬度和高度 (3 * 4 = 12 bytes, 3个float)
        station.setLongitude(readFloat(raf));
        station.setLatitude(readFloat(raf));
        station.setElevation(readFloat(raf));

        // 读取站点值（根据elementCount确定有几个值）
        // 第三类数据的站点值是字符串格式，需要特殊处理
        if (structure.getElementCount() >= 1) {
            // 读取第一个值（假设是4字节float，然后转换为字符串）
            float value1 = readFloat(raf);
            station.setValue1(formatStationValue(value1, structure.getLevel()));
        }

        if (structure.getElementCount() >= 2) {
            // 读取第二个值
            float value2 = readFloat(raf);
            station.setValue2(formatStationValue(value2, structure.getLevel()));
        }

        return station;
    }

    /**
     * 根据层次格式化站点值
     */
    private String formatStationValue(float value, int level) {
        // 根据层次（格式控制标志）格式化数值
        switch (level) {
            case -1: // 6小时降水量格式
                if (value == 0.0f) {
                    return "T";
                } else if (value >= 0.1f && value < 1.0f) {
                    return String.format("%.1f", value);
                } else if (value >= 1.0f) {
                    return String.valueOf((int) value);
                }
                break;
            case -2: // 24小时降水量格式
                if (value < 1.0f) {
                    return ""; // 小于1mm不填
                } else {
                    return String.valueOf((int) value);
                }
            case -3: // 温度格式
                return String.valueOf((int) value);
            default:
                // 普通格式
                if (value == (int) value) {
                    return String.valueOf((int) value);
                } else {
                    return String.format("%.1f", value);
                }
        }
        return String.valueOf(value);
    }
}
